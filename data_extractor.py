import requests
from bs4 import Beautiful<PERSON>oup
import time
import logging
from urllib.parse import urljoin
from fake_useragent import UserAgent
from config import BASE_URL, TIMEOUT, MAX_RETRIES, DELAY_BETWEEN_REQUESTS, USER_AGENTS
from utils import clean_text, get_current_timestamp, safe_sleep

class CertificateExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.logger = logging.getLogger(__name__)

    def get_headers(self):
        """Get random headers"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def extract_certificate_data(self, certificate_id):
        """Extract certificate data from the website"""
        url = f"{BASE_URL}?id={certificate_id}"

        for attempt in range(MAX_RETRIES):
            try:
                self.logger.info(f"Attempting to extract data for ID: {certificate_id} - Attempt {attempt + 1}")

                response = self.session.get(
                    url,
                    headers=self.get_headers(),
                    timeout=TIMEOUT
                )

                if response.status_code == 200:
                    return self._parse_certificate_page(response.text, certificate_id)
                elif response.status_code == 404:
                    self.logger.info(f"Certificate not found: {certificate_id}")
                    return None
                else:
                    self.logger.warning(f"Unexpected response code: {response.status_code}")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request error for ID {certificate_id}: {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    safe_sleep(DELAY_BETWEEN_REQUESTS * (attempt + 1))

        return None

    def _parse_certificate_page(self, html_content, certificate_id):
        """Parse certificate page and extract data"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Search for data on the page
            data = {
                'ID': certificate_id,
                'Student Name': self._extract_student_name(soup),
                'Institute/College': self._extract_college(soup),
                'Certificate Type': self._extract_specialization(soup),
                'Examination Year': self._extract_graduation_year(soup),
                'Student Status': self._extract_grade(soup),
                'National ID': self._extract_certificate_number(soup),
                'Extraction Date': get_current_timestamp(),
                'Image Path': '',
                'Extraction Status': 'Success'
            }

            # Extract image URL
            image_url = self._extract_image_url(soup)
            if image_url:
                data['image_url'] = image_url

            # Extract additional data
            additional_data = self.extract_additional_data(soup)
            data.update(additional_data)

            # Check for student name existence
            student_name = data.get('Student Name', '').strip()
            if not student_name or len(student_name) < 3:
                self.logger.warning(f"No valid name found for ID: {certificate_id}")
                return None

            self.logger.info(f"Data extracted successfully for ID: {certificate_id} - Name: {student_name}")
            return data

        except Exception as e:
            self.logger.error(f"Error parsing page for ID {certificate_id}: {str(e)}")
            return None

    def _extract_student_name(self, soup):
        """Extract student name"""
        # Search for student name based on actual page structure

        # Search in tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'اسم الطالب' in cell_text and i + 1 < len(cells):  # "Student Name" in Arabic
                        name = clean_text(cells[i + 1].get_text())
                        if self._is_valid_name(name):
                            return name

        # Search in direct text
        text = soup.get_text()
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if 'اسم الطالب' in line and i + 1 < len(lines):  # "Student Name" in Arabic
                name = clean_text(lines[i + 1])
                if self._is_valid_name(name):
                    return name

        return ""

    def _is_valid_name(self, name):
        """Validate name"""
        if not name or len(name.strip()) < 3:
            return False

        # Words to avoid in names (Arabic words kept for source compatibility)
        invalid_words = [
            'اسم', 'الطالب', 'رباعياً', 'الاسم', 'باللغة', 'الإنجليزية',
            'name', 'student', 'english', 'arabic', 'null', 'none', 'n/a',
            'غير محدد', 'لا يوجد', 'فارغ', 'empty'
        ]

        name_lower = name.lower().strip()

        # Avoid names containing invalid words
        for word in invalid_words:
            if word in name_lower:
                return False

        # Check for Arabic or English characters
        has_arabic = any('\u0600' <= char <= '\u06FF' for char in name)
        has_english = any('a' <= char.lower() <= 'z' for char in name)

        if not (has_arabic or has_english):
            return False

        # Check for excessive numbers
        digit_count = sum(1 for char in name if char.isdigit())
        if digit_count > len(name) // 2:  # If more than half the name is numbers
            return False

        return True

    def _extract_college(self, soup):
        """Extract institute/college name"""
        # Search for institute in tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'المعهد' in cell_text and i + 1 < len(cells):  # "Institute" in Arabic
                        institute = clean_text(cells[i + 1].get_text())
                        if institute and len(institute) > 2:
                            return institute

        # Search in direct text
        text = soup.get_text()
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if 'المعهد' in line and i + 1 < len(lines):  # "Institute" in Arabic
                institute = clean_text(lines[i + 1])
                if institute and len(institute) > 2:
                    return institute

        return ""

    def _extract_specialization(self, soup):
        """Extract certificate type/specialization"""
        # Search for certificate type
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'الشهاده المتقدم لها' in cell_text and i + 1 < len(cells):  # "Applied Certificate" in Arabic
                        cert_type = clean_text(cells[i + 1].get_text())
                        if cert_type and len(cert_type) > 2:
                            return cert_type

        # Search in direct text
        text = soup.get_text()
        if 'الابتدائية' in text:  # "Primary" in Arabic
            return 'Al-Azhar Primary Certificate'
        elif 'الإعدادية' in text:  # "Preparatory" in Arabic
            return 'Al-Azhar Preparatory Certificate'
        elif 'الثانوية' in text:  # "Secondary" in Arabic
            return 'Al-Azhar Secondary Certificate'

        return ""

    def _extract_graduation_year(self, soup):
        """Extract examination year"""
        # Search in page title or text
        text = soup.get_text()

        # Search for year in title
        import re
        year_pattern = r'20\d{2}\s*/\s*20\d{2}'
        matches = re.findall(year_pattern, text)
        if matches:
            return matches[0]

        # Search for form creation date
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'تاريخ تحرير' in cell_text and i + 1 < len(cells):  # "Creation Date" in Arabic
                        date = clean_text(cells[i + 1].get_text())
                        if date and any(char.isdigit() for char in date):
                            return date

        return ""

    def _extract_grade(self, soup):
        """Extract student status"""
        # Search for enrollment status
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'حالة القيد' in cell_text and i + 1 < len(cells):  # "Enrollment Status" in Arabic
                        status = clean_text(cells[i + 1].get_text())
                        if status and len(status) > 1:
                            return status
                    elif 'مستجد/باق للإعادة' in cell_text and i + 1 < len(cells):  # "New/Repeating" in Arabic
                        status = clean_text(cells[i + 1].get_text())
                        if status and len(status) > 1:
                            return status

        return ""

    def _extract_certificate_number(self, soup):
        """Extract national ID or identity number"""
        # Search for national ID
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'الرقم القومى' in cell_text and i + 1 < len(cells):  # "National ID" in Arabic
                        national_id = clean_text(cells[i + 1].get_text())
                        if national_id and any(char.isdigit() for char in national_id):
                            return national_id

        return ""

    def _extract_image_url(self, soup):
        """Extract student image URL"""
        # Search for student image
        images = soup.find_all('img')
        for img in images:
            src = img.get('src', '')
            # Search for student images (usually in upload/students folder)
            if 'upload/students' in src or 'student' in src.lower():
                if src.startswith('http'):
                    return src
                else:
                    # Build complete URL
                    base_domain = "https://azhar.gov.eg"
                    if src.startswith('/'):
                        return base_domain + src
                    else:
                        return urljoin(BASE_URL, src)

        return None

    def extract_additional_data(self, soup):
        """Extract additional useful data"""
        additional_data = {}

        # Extract birth date
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'تاريخ الميلاد' in cell_text and i + 1 < len(cells):
                        birth_date = clean_text(cells[i + 1].get_text())
                        additional_data['Birth Date'] = birth_date
                    elif 'النوع' in cell_text and i + 1 < len(cells):
                        gender = clean_text(cells[i + 1].get_text())
                        additional_data['Gender'] = gender
                    elif 'محافظة الميلاد' in cell_text and i + 1 < len(cells):
                        birth_gov = clean_text(cells[i + 1].get_text())
                        additional_data['Birth Governorate'] = birth_gov
                    elif 'الإدارة التعليمية' in cell_text and i + 1 < len(cells):
                        education_admin = clean_text(cells[i + 1].get_text())
                        additional_data['Educational Administration'] = education_admin

        return additional_data
