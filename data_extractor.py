import requests
from bs4 import BeautifulSoup
import time
import logging
from urllib.parse import urljoin
from fake_useragent import UserAgent
from config import BASE_URL, TIMEOUT, MAX_RETRIES, DELAY_BETWEEN_REQUESTS, USER_AGENTS
from utils import clean_text, get_current_timestamp, safe_sleep

class CertificateExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.logger = logging.getLogger(__name__)
        
    def get_headers(self):
        """الحصول على headers عشوائية"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def extract_certificate_data(self, certificate_id):
        """استخراج بيانات الشهادة من الموقع"""
        url = f"{BASE_URL}?id={certificate_id}"
        
        for attempt in range(MAX_RETRIES):
            try:
                self.logger.info(f"محاولة استخراج البيانات للرقم: {certificate_id} - المحاولة {attempt + 1}")
                
                response = self.session.get(
                    url,
                    headers=self.get_headers(),
                    timeout=TIMEOUT
                )
                
                if response.status_code == 200:
                    return self._parse_certificate_page(response.text, certificate_id)
                elif response.status_code == 404:
                    self.logger.info(f"الشهادة غير موجودة: {certificate_id}")
                    return None
                else:
                    self.logger.warning(f"رمز استجابة غير متوقع: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"خطأ في الطلب للرقم {certificate_id}: {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    safe_sleep(DELAY_BETWEEN_REQUESTS * (attempt + 1))
                    
        return None
    
    def _parse_certificate_page(self, html_content, certificate_id):
        """تحليل صفحة الشهادة واستخراج البيانات"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # البحث عن البيانات في الصفحة
            data = {
                'ID': certificate_id,
                'اسم الطالب': self._extract_student_name(soup),
                'المعهد/الكلية': self._extract_college(soup),
                'نوع الشهادة': self._extract_specialization(soup),
                'سنة الامتحان': self._extract_graduation_year(soup),
                'حالة الطالب': self._extract_grade(soup),
                'الرقم القومي': self._extract_certificate_number(soup),
                'تاريخ الاستخراج': get_current_timestamp(),
                'مسار الصورة': '',
                'حالة الاستخراج': 'نجح'
            }
            
            # استخراج رابط الصورة
            image_url = self._extract_image_url(soup)
            if image_url:
                data['image_url'] = image_url

            # استخراج البيانات الإضافية
            additional_data = self.extract_additional_data(soup)
            data.update(additional_data)

            # التحقق من وجود اسم الطالب
            student_name = data.get('اسم الطالب', '').strip()
            if not student_name or len(student_name) < 3:
                self.logger.warning(f"لم يتم العثور على اسم صحيح للرقم: {certificate_id}")
                return None

            self.logger.info(f"تم استخراج البيانات بنجاح للرقم: {certificate_id} - الاسم: {student_name}")
            return data
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الصفحة للرقم {certificate_id}: {str(e)}")
            return None

    def _extract_student_name(self, soup):
        """استخراج اسم الطالب"""
        # البحث عن اسم الطالب بناءً على بنية الصفحة الفعلية

        # البحث في الجداول
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'اسم الطالب' in cell_text and i + 1 < len(cells):
                        name = clean_text(cells[i + 1].get_text())
                        if self._is_valid_name(name):
                            return name

        # البحث في النص المباشر
        text = soup.get_text()
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if 'اسم الطالب' in line and i + 1 < len(lines):
                name = clean_text(lines[i + 1])
                if self._is_valid_name(name):
                    return name

        return ""

    def _is_valid_name(self, name):
        """التحقق من صحة الاسم"""
        if not name or len(name.strip()) < 3:
            return False

        # كلمات يجب تجنبها في الاسم
        invalid_words = [
            'اسم', 'الطالب', 'رباعياً', 'الاسم', 'باللغة', 'الإنجليزية',
            'name', 'student', 'english', 'arabic', 'null', 'none', 'n/a',
            'غير محدد', 'لا يوجد', 'فارغ', 'empty'
        ]

        name_lower = name.lower().strip()

        # تجنب الأسماء التي تحتوي على كلمات غير صحيحة
        for word in invalid_words:
            if word in name_lower:
                return False

        # التحقق من وجود أحرف عربية أو إنجليزية
        has_arabic = any('\u0600' <= char <= '\u06FF' for char in name)
        has_english = any('a' <= char.lower() <= 'z' for char in name)

        if not (has_arabic or has_english):
            return False

        # التحقق من عدم وجود أرقام كثيرة
        digit_count = sum(1 for char in name if char.isdigit())
        if digit_count > len(name) // 2:  # إذا كان أكثر من نصف الاسم أرقام
            return False

        return True

    def _extract_college(self, soup):
        """استخراج اسم المعهد/الكلية"""
        # البحث عن المعهد في الجداول
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'المعهد' in cell_text and i + 1 < len(cells):
                        institute = clean_text(cells[i + 1].get_text())
                        if institute and len(institute) > 2:
                            return institute

        # البحث في النص المباشر
        text = soup.get_text()
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if 'المعهد' in line and i + 1 < len(lines):
                institute = clean_text(lines[i + 1])
                if institute and len(institute) > 2:
                    return institute

        return ""

    def _extract_specialization(self, soup):
        """استخراج نوع الشهادة/التخصص"""
        # البحث عن نوع الشهادة
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'الشهاده المتقدم لها' in cell_text and i + 1 < len(cells):
                        cert_type = clean_text(cells[i + 1].get_text())
                        if cert_type and len(cert_type) > 2:
                            return cert_type

        # البحث في النص المباشر
        text = soup.get_text()
        if 'الابتدائية' in text:
            return 'الشهادة الابتدائية الأزهرية'
        elif 'الإعدادية' in text:
            return 'الشهادة الإعدادية الأزهرية'
        elif 'الثانوية' in text:
            return 'الشهادة الثانوية الأزهرية'

        return ""

    def _extract_graduation_year(self, soup):
        """استخراج سنة الامتحان"""
        # البحث في عنوان الصفحة أو النص
        text = soup.get_text()

        # البحث عن السنة في العنوان
        import re
        year_pattern = r'20\d{2}\s*/\s*20\d{2}'
        matches = re.findall(year_pattern, text)
        if matches:
            return matches[0]

        # البحث عن تاريخ تحرير الاستمارة
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'تاريخ تحرير' in cell_text and i + 1 < len(cells):
                        date = clean_text(cells[i + 1].get_text())
                        if date and any(char.isdigit() for char in date):
                            return date

        return ""

    def _extract_grade(self, soup):
        """استخراج حالة الطالب"""
        # البحث عن حالة القيد
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'حالة القيد' in cell_text and i + 1 < len(cells):
                        status = clean_text(cells[i + 1].get_text())
                        if status and len(status) > 1:
                            return status
                    elif 'مستجد/باق للإعادة' in cell_text and i + 1 < len(cells):
                        status = clean_text(cells[i + 1].get_text())
                        if status and len(status) > 1:
                            return status

        return ""

    def _extract_certificate_number(self, soup):
        """استخراج الرقم القومي أو رقم الهوية"""
        # البحث عن الرقم القومي
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'الرقم القومى' in cell_text and i + 1 < len(cells):
                        national_id = clean_text(cells[i + 1].get_text())
                        if national_id and any(char.isdigit() for char in national_id):
                            return national_id

        return ""

    def _extract_image_url(self, soup):
        """استخراج رابط صورة الطالب"""
        # البحث عن صورة الطالب
        images = soup.find_all('img')
        for img in images:
            src = img.get('src', '')
            # البحث عن صور الطلاب (عادة في مجلد upload/students)
            if 'upload/students' in src or 'student' in src.lower():
                if src.startswith('http'):
                    return src
                else:
                    # تكوين الرابط الكامل
                    base_domain = "https://azhar.gov.eg"
                    if src.startswith('/'):
                        return base_domain + src
                    else:
                        return urljoin(BASE_URL, src)

        return None

    def extract_additional_data(self, soup):
        """استخراج بيانات إضافية مفيدة"""
        additional_data = {}

        # استخراج تاريخ الميلاد
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                for i, cell in enumerate(cells):
                    cell_text = clean_text(cell.get_text())
                    if 'تاريخ الميلاد' in cell_text and i + 1 < len(cells):
                        birth_date = clean_text(cells[i + 1].get_text())
                        additional_data['تاريخ الميلاد'] = birth_date
                    elif 'النوع' in cell_text and i + 1 < len(cells):
                        gender = clean_text(cells[i + 1].get_text())
                        additional_data['النوع'] = gender
                    elif 'محافظة الميلاد' in cell_text and i + 1 < len(cells):
                        birth_gov = clean_text(cells[i + 1].get_text())
                        additional_data['محافظة الميلاد'] = birth_gov
                    elif 'الإدارة التعليمية' in cell_text and i + 1 < len(cells):
                        education_admin = clean_text(cells[i + 1].get_text())
                        additional_data['الإدارة التعليمية'] = education_admin

        return additional_data
