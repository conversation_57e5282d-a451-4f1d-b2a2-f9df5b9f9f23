import os
import requests
from PIL import Image
import logging
from urllib.parse import urlparse
from config import IMAGES_DIR, TIMEOUT
from utils import safe_sleep

class ImageProcessor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
    
    def download_certificate_image(self, image_url, certificate_id):
        """تحميل صورة الشهادة وحفظها"""
        if not image_url:
            self.logger.warning(f"لا يوجد رابط صورة للشهادة: {certificate_id}")
            return None
        
        try:
            # تحديد اسم الملف
            parsed_url = urlparse(image_url)
            file_extension = self._get_file_extension(parsed_url.path, image_url)
            filename = f"certificate_{certificate_id}{file_extension}"
            file_path = os.path.join(IMAGES_DIR, filename)
            
            # تحميل الصورة
            self.logger.info(f"تحميل صورة الشهادة: {certificate_id}")
            response = self.session.get(image_url, timeout=TIMEOUT, stream=True)
            response.raise_for_status()
            
            # حفظ الصورة
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # التحقق من صحة الصورة وتحسينها
            optimized_path = self._optimize_image(file_path, certificate_id)
            
            self.logger.info(f"تم حفظ صورة الشهادة: {optimized_path}")
            return optimized_path
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"خطأ في تحميل صورة الشهادة {certificate_id}: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"خطأ عام في معالجة صورة الشهادة {certificate_id}: {str(e)}")
            return None
    
    def _get_file_extension(self, url_path, full_url):
        """تحديد امتداد الملف"""
        # محاولة استخراج الامتداد من المسار
        if url_path and '.' in url_path:
            extension = os.path.splitext(url_path)[1].lower()
            if extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                return extension
        
        # محاولة تحديد النوع من المحتوى
        try:
            response = self.session.head(full_url, timeout=10)
            content_type = response.headers.get('content-type', '').lower()
            
            if 'jpeg' in content_type or 'jpg' in content_type:
                return '.jpg'
            elif 'png' in content_type:
                return '.png'
            elif 'gif' in content_type:
                return '.gif'
            elif 'bmp' in content_type:
                return '.bmp'
        except:
            pass
        
        # افتراضي
        return '.jpg'
    
    def _optimize_image(self, image_path, certificate_id):
        """تحسين وضغط الصورة"""
        try:
            with Image.open(image_path) as img:
                # التحقق من صحة الصورة
                img.verify()
                
                # إعادة فتح الصورة للمعالجة
                with Image.open(image_path) as img:
                    # تحويل إلى RGB إذا لزم الأمر
                    if img.mode in ('RGBA', 'LA', 'P'):
                        img = img.convert('RGB')
                    
                    # تحسين الحجم إذا كانت الصورة كبيرة جداً
                    max_size = (1920, 1080)
                    if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                        img.thumbnail(max_size, Image.Resampling.LANCZOS)
                        self.logger.info(f"تم تصغير حجم الصورة للشهادة: {certificate_id}")
                    
                    # حفظ الصورة المحسنة
                    optimized_path = os.path.splitext(image_path)[0] + '_optimized.jpg'
                    img.save(optimized_path, 'JPEG', quality=85, optimize=True)
                    
                    # حذف الصورة الأصلية إذا كانت مختلفة
                    if optimized_path != image_path:
                        try:
                            os.remove(image_path)
                        except:
                            pass
                    
                    return optimized_path
                    
        except Exception as e:
            self.logger.error(f"خطأ في تحسين صورة الشهادة {certificate_id}: {str(e)}")
            return image_path  # إرجاع المسار الأصلي في حالة الخطأ
    
    def create_thumbnail(self, image_path, size=(200, 200)):
        """إنشاء صورة مصغرة"""
        try:
            with Image.open(image_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                thumbnail_path = os.path.splitext(image_path)[0] + '_thumb.jpg'
                img.save(thumbnail_path, 'JPEG', quality=80)
                
                return thumbnail_path
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الصورة المصغرة: {str(e)}")
            return None
    
    def get_image_info(self, image_path):
        """الحصول على معلومات الصورة"""
        try:
            with Image.open(image_path) as img:
                return {
                    'size': img.size,
                    'mode': img.mode,
                    'format': img.format,
                    'file_size': os.path.getsize(image_path)
                }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الصورة: {str(e)}")
            return None
