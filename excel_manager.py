import os
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import logging
from config import EXCEL_FILE, EXCEL_COLUMNS
from utils import get_current_timestamp

# محاولة استيراد Image من openpyxl
try:
    from openpyxl.drawing.image import Image as ExcelImage
except ImportError:
    try:
        from openpyxl.drawing import Image as ExcelImage
    except ImportError:
        ExcelImage = None

class ExcelManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.excel_file = EXCEL_FILE
        self.columns = EXCEL_COLUMNS
        
    def initialize_excel_file(self):
        """إنشاء ملف Excel جديد مع التنسيق"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "بيانات الشهادات"
            
            # إضافة العناوين
            for col_num, column_title in enumerate(self.columns, 1):
                cell = ws.cell(row=1, column=col_num)
                cell.value = column_title
                cell.font = Font(bold=True, size=12)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.font = Font(bold=True, size=12, color="FFFFFF")
            
            # تنسيق العرض
            column_widths = [10, 25, 20, 20, 15, 15, 15, 20, 30, 15]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width
            
            # حفظ الملف
            wb.save(self.excel_file)
            self.logger.info(f"تم إنشاء ملف Excel: {self.excel_file}")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملف Excel: {str(e)}")
            raise
    
    def add_certificate_data(self, data, image_path=None):
        """إضافة بيانات شهادة جديدة إلى Excel"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(self.excel_file):
                self.initialize_excel_file()
            
            # تحميل الملف
            wb = load_workbook(self.excel_file)
            ws = wb.active
            
            # العثور على الصف التالي الفارغ
            next_row = ws.max_row + 1
            
            # إضافة البيانات
            for col_num, column_name in enumerate(self.columns, 1):
                cell = ws.cell(row=next_row, column=col_num)
                
                if column_name == 'مسار الصورة' and image_path:
                    cell.value = image_path
                else:
                    cell.value = data.get(column_name, "")
                
                # تنسيق الخلية
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # إضافة الصورة إذا كانت متوفرة
            if image_path and os.path.exists(image_path):
                self._add_image_to_excel(ws, image_path, next_row)
            
            # حفظ الملف
            wb.save(self.excel_file)
            self.logger.info(f"تم إضافة بيانات الشهادة رقم: {data.get('ID', 'غير محدد')}")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة البيانات إلى Excel: {str(e)}")
            raise
    
    def _add_image_to_excel(self, worksheet, image_path, row_number):
        """إضافة صورة إلى Excel"""
        try:
            if ExcelImage is None:
                self.logger.warning("مكتبة الصور غير متاحة في openpyxl")
                return

            # إنشاء صورة Excel
            img = ExcelImage(image_path)

            # تحديد حجم الصورة
            img.width = 100
            img.height = 100

            # تحديد موقع الصورة (في عمود منفصل)
            img_column = len(self.columns) + 1
            cell_address = f"{worksheet.cell(row=row_number, column=img_column).coordinate}"

            # إضافة الصورة
            worksheet.add_image(img, cell_address)

            # تعديل ارتفاع الصف
            worksheet.row_dimensions[row_number].height = 75

        except Exception as e:
            self.logger.error(f"خطأ في إضافة الصورة إلى Excel: {str(e)}")
    
    def add_multiple_certificates(self, certificates_data):
        """إضافة عدة شهادات دفعة واحدة"""
        try:
            for cert_data in certificates_data:
                image_path = cert_data.get('image_path')
                self.add_certificate_data(cert_data, image_path)
                
            self.logger.info(f"تم إضافة {len(certificates_data)} شهادة إلى Excel")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الشهادات المتعددة: {str(e)}")
            raise
    
    def get_existing_ids(self):
        """الحصول على قائمة بالأرقام الموجودة بالفعل"""
        try:
            if not os.path.exists(self.excel_file):
                return set()
            
            df = pd.read_excel(self.excel_file)
            if 'ID' in df.columns:
                return set(df['ID'].dropna().astype(str))
            return set()
            
        except Exception as e:
            self.logger.error(f"خطأ في قراءة الأرقام الموجودة: {str(e)}")
            return set()
    
    def create_summary_sheet(self):
        """إنشاء ورقة ملخص الإحصائيات"""
        try:
            wb = load_workbook(self.excel_file)
            
            # إنشاء ورقة جديدة للملخص
            if "الملخص" in wb.sheetnames:
                wb.remove(wb["الملخص"])
            
            summary_ws = wb.create_sheet("الملخص")
            
            # قراءة البيانات
            df = pd.read_excel(self.excel_file, sheet_name="بيانات الشهادات")
            
            # إحصائيات أساسية
            stats = [
                ["إجمالي الشهادات", len(df)],
                ["الشهادات الناجحة", len(df[df['حالة الاستخراج'] == 'نجح'])],
                ["تاريخ آخر تحديث", get_current_timestamp()],
                ["", ""],
                ["إحصائيات الكليات", ""],
            ]
            
            # إحصائيات الكليات
            if 'الكلية' in df.columns:
                college_counts = df['الكلية'].value_counts()
                for college, count in college_counts.items():
                    stats.append([college, count])
            
            # كتابة الإحصائيات
            for row_num, (label, value) in enumerate(stats, 1):
                summary_ws.cell(row=row_num, column=1, value=label)
                summary_ws.cell(row=row_num, column=2, value=value)
            
            # تنسيق الملخص
            for row in summary_ws.iter_rows(min_row=1, max_row=len(stats)):
                for cell in row:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    if row[0].row <= 3:  # العناوين الرئيسية
                        cell.font = Font(bold=True, size=12)
            
            wb.save(self.excel_file)
            self.logger.info("تم إنشاء ورقة الملخص")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ورقة الملخص: {str(e)}")
    
    def export_to_csv(self, csv_file=None):
        """تصدير البيانات إلى CSV"""
        try:
            if not csv_file:
                csv_file = self.excel_file.replace('.xlsx', '.csv')
            
            df = pd.read_excel(self.excel_file, sheet_name="بيانات الشهادات")
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"تم تصدير البيانات إلى: {csv_file}")
            return csv_file
            
        except Exception as e:
            self.logger.error(f"خطأ في التصدير إلى CSV: {str(e)}")
            return None
