import os
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import logging
from config import EXCEL_FILE, EXCEL_COLUMNS
from utils import get_current_timestamp

# Try to import Image from openpyxl
try:
    from openpyxl.drawing.image import Image as ExcelImage
except ImportError:
    try:
        from openpyxl.drawing import Image as ExcelImage
    except ImportError:
        ExcelImage = None

class ExcelManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.excel_file = EXCEL_FILE
        self.columns = EXCEL_COLUMNS

    def initialize_excel_file(self):
        """Create new Excel file with formatting"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "Certificate Data"

            # Add headers
            for col_num, column_title in enumerate(self.columns, 1):
                cell = ws.cell(row=1, column=col_num)
                cell.value = column_title
                cell.font = Font(bold=True, size=12)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.font = Font(bold=True, size=12, color="FFFFFF")

            # Format width
            column_widths = [10, 25, 20, 20, 15, 15, 15, 20, 30, 15]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width

            # Save file
            wb.save(self.excel_file)
            self.logger.info(f"Excel file created: {self.excel_file}")

        except Exception as e:
            self.logger.error(f"Error creating Excel file: {str(e)}")
            raise

    def add_certificate_data(self, data, image_path=None):
        """Add new certificate data to Excel"""
        try:
            # Check if file exists
            if not os.path.exists(self.excel_file):
                self.initialize_excel_file()

            # Load file
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # Find next empty row
            next_row = ws.max_row + 1

            # Add data
            for col_num, column_name in enumerate(self.columns, 1):
                cell = ws.cell(row=next_row, column=col_num)

                if column_name == 'Image Path' and image_path:
                    cell.value = image_path
                else:
                    cell.value = data.get(column_name, "")

                # Format cell
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

            # Add image if available
            if image_path and os.path.exists(image_path):
                self._add_image_to_excel(ws, image_path, next_row)

            # Save file
            wb.save(self.excel_file)
            self.logger.info(f"Certificate data added for ID: {data.get('ID', 'Not specified')}")

        except Exception as e:
            self.logger.error(f"Error adding data to Excel: {str(e)}")
            raise

    def _add_image_to_excel(self, worksheet, image_path, row_number):
        """Add image to Excel"""
        try:
            if ExcelImage is None:
                self.logger.warning("Image library not available in openpyxl")
                return

            # Create Excel image
            img = ExcelImage(image_path)

            # Set image size
            img.width = 100
            img.height = 100

            # Set image location (in separate column)
            img_column = len(self.columns) + 1
            cell_address = f"{worksheet.cell(row=row_number, column=img_column).coordinate}"

            # Add image
            worksheet.add_image(img, cell_address)

            # Adjust row height
            worksheet.row_dimensions[row_number].height = 75

        except Exception as e:
            self.logger.error(f"Error adding image to Excel: {str(e)}")

    def add_multiple_certificates(self, certificates_data):
        """Add multiple certificates at once"""
        try:
            for cert_data in certificates_data:
                image_path = cert_data.get('image_path')
                self.add_certificate_data(cert_data, image_path)

            self.logger.info(f"Added {len(certificates_data)} certificates to Excel")

        except Exception as e:
            self.logger.error(f"Error adding multiple certificates: {str(e)}")
            raise

    def get_existing_ids(self):
        """Get list of existing IDs"""
        try:
            if not os.path.exists(self.excel_file):
                return set()

            df = pd.read_excel(self.excel_file)
            if 'ID' in df.columns:
                return set(df['ID'].dropna().astype(str))
            return set()

        except Exception as e:
            self.logger.error(f"Error reading existing IDs: {str(e)}")
            return set()

    def create_summary_sheet(self):
        """Create statistics summary sheet"""
        try:
            wb = load_workbook(self.excel_file)

            # Create new summary sheet
            if "Summary" in wb.sheetnames:
                wb.remove(wb["Summary"])

            summary_ws = wb.create_sheet("Summary")

            # Read data
            df = pd.read_excel(self.excel_file, sheet_name="Certificate Data")

            # Basic statistics
            stats = [
                ["Total Certificates", len(df)],
                ["Successful Certificates", len(df[df['Extraction Status'] == 'Success'])],
                ["Last Update", get_current_timestamp()],
                ["", ""],
                ["College Statistics", ""],
            ]

            # College statistics
            if 'Institute/College' in df.columns:
                college_counts = df['Institute/College'].value_counts()
                for college, count in college_counts.items():
                    stats.append([college, count])

            # Write statistics
            for row_num, (label, value) in enumerate(stats, 1):
                summary_ws.cell(row=row_num, column=1, value=label)
                summary_ws.cell(row=row_num, column=2, value=value)

            # Format summary
            for row in summary_ws.iter_rows(min_row=1, max_row=len(stats)):
                for cell in row:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    if row[0].row <= 3:  # Main headers
                        cell.font = Font(bold=True, size=12)

            wb.save(self.excel_file)
            self.logger.info("Summary sheet created")

        except Exception as e:
            self.logger.error(f"Error creating summary sheet: {str(e)}")

    def export_to_csv(self, csv_file=None):
        """Export data to CSV"""
        try:
            if not csv_file:
                csv_file = self.excel_file.replace('.xlsx', '.csv')

            df = pd.read_excel(self.excel_file, sheet_name="Certificate Data")
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')

            self.logger.info(f"Data exported to: {csv_file}")
            return csv_file

        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {str(e)}")
            return None
