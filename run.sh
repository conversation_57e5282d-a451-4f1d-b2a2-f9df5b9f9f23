#!/bin/bash

# Set encoding
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# Text colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Banner printing function
print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                Al-Azhar Certificate Extraction Tool          ║"
    echo "║                   For Authorized Government Use              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Main menu function
show_menu() {
    echo -e "${YELLOW}Choose the required operation:${NC}"
    echo "1. Install requirements"
    echo "2. Run main tool"
    echo "3. Run tests"
    echo "4. Run quick test"
    echo "5. Exit"
    echo
}

# Installation function
install_requirements() {
    echo -e "${BLUE}Installing requirements...${NC}"
    python3 install.py
    read -p "Press Enter to continue..."
}

# Main tool execution function
run_main() {
    echo -e "${BLUE}Running main tool...${NC}"
    python3 main.py
    read -p "Press Enter to continue..."
}

# Tests execution function
run_tests() {
    echo -e "${BLUE}Running tests...${NC}"
    python3 run_tests.py
    read -p "Press Enter to continue..."
}

# Quick test execution function
run_quick_test() {
    echo -e "${BLUE}Running quick test...${NC}"
    python3 quick_test.py
    read -p "Press Enter to continue..."
}

# Check Python availability
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}Error: Python 3 is not installed${NC}"
        echo "Please install Python 3 first"
        exit 1
    fi
}

# Main function
main() {
    clear
    print_banner
    check_python

    while true; do
        echo
        show_menu
        read -p "Your choice (1-5): " choice

        case $choice in
            1)
                install_requirements
                ;;
            2)
                run_main
                ;;
            3)
                run_tests
                ;;
            4)
                run_quick_test
                ;;
            5)
                echo -e "${GREEN}Goodbye!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}Invalid choice, please try again.${NC}"
                read -p "Press Enter to continue..."
                ;;
        esac

        clear
        print_banner
    done
}

# Run main function
main
