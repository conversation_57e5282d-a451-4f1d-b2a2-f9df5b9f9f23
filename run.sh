#!/bin/bash

# تعيين الترميز
export LANG=ar_EG.UTF-8
export LC_ALL=ar_EG.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# دالة طباعة الشعار
print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    أداة استخراج شهادات الأزهر                    ║"
    echo "║                     للاستخدام الحكومي المصرح                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# دالة القائمة الرئيسية
show_menu() {
    echo -e "${YELLOW}اختر العملية المطلوبة:${NC}"
    echo "1. تثبيت المتطلبات"
    echo "2. تشغيل الأداة الرئيسية"
    echo "3. تشغيل الاختبارات"
    echo "4. تشغيل اختبار سريع"
    echo "5. خروج"
    echo
}

# دالة التثبيت
install_requirements() {
    echo -e "${BLUE}تثبيت المتطلبات...${NC}"
    python3 install.py
    read -p "اضغط Enter للمتابعة..."
}

# دالة تشغيل الأداة الرئيسية
run_main() {
    echo -e "${BLUE}تشغيل الأداة الرئيسية...${NC}"
    python3 main.py
    read -p "اضغط Enter للمتابعة..."
}

# دالة تشغيل الاختبارات
run_tests() {
    echo -e "${BLUE}تشغيل الاختبارات...${NC}"
    python3 run_tests.py
    read -p "اضغط Enter للمتابعة..."
}

# دالة تشغيل الاختبار السريع
run_quick_test() {
    echo -e "${BLUE}تشغيل اختبار سريع...${NC}"
    python3 quick_test.py
    read -p "اضغط Enter للمتابعة..."
}

# التحقق من وجود Python
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}خطأ: Python 3 غير مثبت${NC}"
        echo "يرجى تثبيت Python 3 أولاً"
        exit 1
    fi
}

# الدالة الرئيسية
main() {
    clear
    print_banner
    check_python
    
    while true; do
        echo
        show_menu
        read -p "اختيارك (1-5): " choice
        
        case $choice in
            1)
                install_requirements
                ;;
            2)
                run_main
                ;;
            3)
                run_tests
                ;;
            4)
                run_quick_test
                ;;
            5)
                echo -e "${GREEN}وداعاً!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}اختيار غير صحيح، حاول مرة أخرى.${NC}"
                read -p "اضغط Enter للمتابعة..."
                ;;
        esac
        
        clear
        print_banner
    done
}

# تشغيل الدالة الرئيسية
main
