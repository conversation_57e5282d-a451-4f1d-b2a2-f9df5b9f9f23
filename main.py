#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Al-Azhar Certificate Data Extraction Tool
Development: For authorized government use
"""

import sys
import time
import random
import threading
from tqdm import tqdm
import logging

from config import MIN_ID, MAX_ID, DELAY_BETWEEN_REQUESTS
from utils import setup_directories, setup_logging, generate_random_id, validate_id
from data_extractor import CertificateExtractor
from image_processor import ImageProcessor
from excel_manager import ExcelManager

class CertificateHarvester:
    def __init__(self):
        self.logger = setup_logging()
        self.extractor = CertificateExtractor()
        self.image_processor = ImageProcessor()
        self.excel_manager = ExcelManager()
        self.is_running = False
        self.stats = {
            'total_attempts': 0,
            'successful_extractions': 0,
            'failed_attempts': 0,
            'no_name_found': 0,
            'images_downloaded': 0
        }

    def start_extraction(self, num_attempts=1000, random_mode=True, start_id=None, end_id=None):
        """Start the extraction process"""
        try:
            setup_directories()
            self.excel_manager.initialize_excel_file()

            self.logger.info("Starting certificate data extraction process")
            self.logger.info(f"Number of attempts required: {num_attempts}")

            self.is_running = True
            existing_ids = self.excel_manager.get_existing_ids()

            # Create progress bar
            progress_bar = tqdm(total=num_attempts, desc="Data Extraction", unit="attempt")

            for attempt in range(num_attempts):
                if not self.is_running:
                    break

                # Generate or determine ID
                if random_mode:
                    certificate_id = self._generate_unique_id(existing_ids)
                else:
                    if start_id is None:
                        start_id = MIN_ID
                    certificate_id = start_id + attempt
                    if end_id and certificate_id > end_id:
                        break

                # Attempt to extract data
                result = self._process_certificate(certificate_id, existing_ids)

                # Update statistics
                self.stats['total_attempts'] += 1
                if result == 'success':
                    self.stats['successful_extractions'] += 1
                    existing_ids.add(str(certificate_id))
                elif result == 'no_name':
                    self.stats['no_name_found'] += 1
                else:
                    self.stats['failed_attempts'] += 1

                # Update progress bar
                progress_bar.set_postfix({
                    'Success': self.stats['successful_extractions'],
                    'Failed': self.stats['failed_attempts'],
                    'No Name': self.stats['no_name_found'],
                    'Images': self.stats['images_downloaded']
                })
                progress_bar.update(1)

                # Delay between requests
                if attempt < num_attempts - 1:
                    time.sleep(DELAY_BETWEEN_REQUESTS)

            progress_bar.close()
            self._finalize_extraction()

        except KeyboardInterrupt:
            self.logger.info("Process stopped by user")
            self.stop_extraction()
        except Exception as e:
            self.logger.error(f"Error in extraction process: {str(e)}")
            raise

    def _generate_unique_id(self, existing_ids, max_attempts=100):
        """Generate a unique ID that doesn't exist previously"""
        for _ in range(max_attempts):
            new_id = generate_random_id(MIN_ID, MAX_ID)
            if str(new_id) not in existing_ids:
                return new_id

        # If we didn't find a unique ID, generate randomly
        return generate_random_id(MIN_ID, MAX_ID)

    def _process_certificate(self, certificate_id, existing_ids):
        """Process a single certificate"""
        try:
            # Skip if already exists
            if str(certificate_id) in existing_ids:
                return 'duplicate'

            # Extract data
            cert_data = self.extractor.extract_certificate_data(certificate_id)

            if cert_data is None:
                # Check reason for failure
                return 'no_name'  # No valid name found

            if cert_data:
                # Download image if available
                image_path = None
                if 'image_url' in cert_data:
                    image_path = self.image_processor.download_certificate_image(
                        cert_data['image_url'], certificate_id
                    )
                    if image_path:
                        self.stats['images_downloaded'] += 1
                        cert_data['Image Path'] = image_path

                # Save to Excel
                self.excel_manager.add_certificate_data(cert_data, image_path)

                student_name = cert_data.get('Student Name', 'Not specified')
                self.logger.info(f"Certificate data extracted successfully: {certificate_id} - {student_name}")
                return 'success'

            return 'failed'

        except Exception as e:
            self.logger.error(f"Error processing certificate {certificate_id}: {str(e)}")
            return 'error'

    def stop_extraction(self):
        """Stop the extraction process"""
        self.is_running = False
        self.logger.info("Process stop requested")

    def _finalize_extraction(self):
        """Finalize the extraction process and create reports"""
        try:
            # Create summary sheet
            self.excel_manager.create_summary_sheet()

            # Print final statistics
            self.logger.info("=== Final Extraction Statistics ===")
            self.logger.info(f"Total attempts: {self.stats['total_attempts']}")
            self.logger.info(f"Successful extractions (with names): {self.stats['successful_extractions']}")
            self.logger.info(f"Failed attempts: {self.stats['failed_attempts']}")
            self.logger.info(f"Certificates without names (ignored): {self.stats['no_name_found']}")
            self.logger.info(f"Images downloaded: {self.stats['images_downloaded']}")

            if self.stats['total_attempts'] > 0:
                success_rate = (self.stats['successful_extractions'] / self.stats['total_attempts']) * 100
                self.logger.info(f"Success rate (with names): {success_rate:.2f}%")

                valid_attempts = self.stats['total_attempts'] - self.stats['no_name_found']
                if valid_attempts > 0:
                    valid_success_rate = (self.stats['successful_extractions'] / valid_attempts) * 100
                    self.logger.info(f"Success rate (from valid certificates): {valid_success_rate:.2f}%")

            self.logger.info("Extraction process completed successfully")

        except Exception as e:
            self.logger.error(f"Error finalizing process: {str(e)}")

    def get_stats(self):
        """Get current statistics"""
        return self.stats.copy()

def print_banner():
    """Print tool banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                Al-Azhar Certificate Extraction Tool          ║
    ║                   For Authorized Government Use              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """Main function"""
    print_banner()

    try:
        harvester = CertificateHarvester()

        print("\nExecution Options:")
        print("1. Random extraction")
        print("2. Sequential extraction")
        print("3. Specific range extraction")

        choice = input("\nChoose extraction type (1-3): ").strip()

        if choice == "1":
            num_attempts = int(input("Number of random attempts: "))
            harvester.start_extraction(num_attempts=num_attempts, random_mode=True)

        elif choice == "2":
            start_id = int(input(f"Start ID (default {MIN_ID}): ") or MIN_ID)
            num_attempts = int(input("Number of attempts: "))
            harvester.start_extraction(num_attempts=num_attempts, random_mode=False, start_id=start_id)

        elif choice == "3":
            start_id = int(input("Start ID: "))
            end_id = int(input("End ID: "))
            num_attempts = end_id - start_id + 1
            harvester.start_extraction(num_attempts=num_attempts, random_mode=False,
                                     start_id=start_id, end_id=end_id)
        else:
            print("Invalid option")
            return

    except KeyboardInterrupt:
        print("\n\nProgram stopped by user")
    except Exception as e:
        print(f"\nError running program: {str(e)}")

if __name__ == "__main__":
    main()
