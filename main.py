#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة استخراج بيانات شهادات الأزهر
تطوير: للاستخدام الحكومي المصرح به
"""

import sys
import time
import random
import threading
from tqdm import tqdm
import logging

from config import MIN_ID, MAX_ID, DELAY_BETWEEN_REQUESTS
from utils import setup_directories, setup_logging, generate_random_id, validate_id
from data_extractor import CertificateExtractor
from image_processor import ImageProcessor
from excel_manager import ExcelManager

class CertificateHarvester:
    def __init__(self):
        self.logger = setup_logging()
        self.extractor = CertificateExtractor()
        self.image_processor = ImageProcessor()
        self.excel_manager = ExcelManager()
        self.is_running = False
        self.stats = {
            'total_attempts': 0,
            'successful_extractions': 0,
            'failed_attempts': 0,
            'no_name_found': 0,
            'images_downloaded': 0
        }
    
    def start_extraction(self, num_attempts=1000, random_mode=True, start_id=None, end_id=None):
        """بدء عملية الاستخراج"""
        try:
            setup_directories()
            self.excel_manager.initialize_excel_file()
            
            self.logger.info("بدء عملية استخراج بيانات الشهادات")
            self.logger.info(f"عدد المحاولات المطلوبة: {num_attempts}")
            
            self.is_running = True
            existing_ids = self.excel_manager.get_existing_ids()
            
            # إنشاء شريط التقدم
            progress_bar = tqdm(total=num_attempts, desc="استخراج البيانات", unit="محاولة")
            
            for attempt in range(num_attempts):
                if not self.is_running:
                    break
                
                # توليد أو تحديد ID
                if random_mode:
                    certificate_id = self._generate_unique_id(existing_ids)
                else:
                    if start_id is None:
                        start_id = MIN_ID
                    certificate_id = start_id + attempt
                    if end_id and certificate_id > end_id:
                        break
                
                # محاولة استخراج البيانات
                result = self._process_certificate(certificate_id, existing_ids)

                # تحديث الإحصائيات
                self.stats['total_attempts'] += 1
                if result == 'success':
                    self.stats['successful_extractions'] += 1
                    existing_ids.add(str(certificate_id))
                elif result == 'no_name':
                    self.stats['no_name_found'] += 1
                else:
                    self.stats['failed_attempts'] += 1
                
                # تحديث شريط التقدم
                progress_bar.set_postfix({
                    'نجح': self.stats['successful_extractions'],
                    'فشل': self.stats['failed_attempts'],
                    'بلا اسم': self.stats['no_name_found'],
                    'صور': self.stats['images_downloaded']
                })
                progress_bar.update(1)
                
                # توقف بين الطلبات
                if attempt < num_attempts - 1:
                    time.sleep(DELAY_BETWEEN_REQUESTS)
            
            progress_bar.close()
            self._finalize_extraction()
            
        except KeyboardInterrupt:
            self.logger.info("تم إيقاف العملية بواسطة المستخدم")
            self.stop_extraction()
        except Exception as e:
            self.logger.error(f"خطأ في عملية الاستخراج: {str(e)}")
            raise
    
    def _generate_unique_id(self, existing_ids, max_attempts=100):
        """توليد ID فريد غير موجود مسبقاً"""
        for _ in range(max_attempts):
            new_id = generate_random_id(MIN_ID, MAX_ID)
            if str(new_id) not in existing_ids:
                return new_id
        
        # إذا لم نجد ID فريد، نولد عشوائياً
        return generate_random_id(MIN_ID, MAX_ID)
    
    def _process_certificate(self, certificate_id, existing_ids):
        """معالجة شهادة واحدة"""
        try:
            # تخطي إذا كان موجود مسبقاً
            if str(certificate_id) in existing_ids:
                return 'duplicate'

            # استخراج البيانات
            cert_data = self.extractor.extract_certificate_data(certificate_id)

            if cert_data is None:
                # التحقق من سبب الفشل
                return 'no_name'  # لم يتم العثور على اسم صحيح

            if cert_data:
                # تحميل الصورة إذا كانت متوفرة
                image_path = None
                if 'image_url' in cert_data:
                    image_path = self.image_processor.download_certificate_image(
                        cert_data['image_url'], certificate_id
                    )
                    if image_path:
                        self.stats['images_downloaded'] += 1
                        cert_data['مسار الصورة'] = image_path

                # حفظ في Excel
                self.excel_manager.add_certificate_data(cert_data, image_path)

                student_name = cert_data.get('اسم الطالب', 'غير محدد')
                self.logger.info(f"تم استخراج بيانات الشهادة بنجاح: {certificate_id} - {student_name}")
                return 'success'

            return 'failed'

        except Exception as e:
            self.logger.error(f"خطأ في معالجة الشهادة {certificate_id}: {str(e)}")
            return 'error'
    
    def stop_extraction(self):
        """إيقاف عملية الاستخراج"""
        self.is_running = False
        self.logger.info("تم طلب إيقاف العملية")
    
    def _finalize_extraction(self):
        """إنهاء عملية الاستخراج وإنشاء التقارير"""
        try:
            # إنشاء ورقة الملخص
            self.excel_manager.create_summary_sheet()
            
            # طباعة الإحصائيات النهائية
            self.logger.info("=== إحصائيات الاستخراج النهائية ===")
            self.logger.info(f"إجمالي المحاولات: {self.stats['total_attempts']}")
            self.logger.info(f"الاستخراجات الناجحة (مع أسماء): {self.stats['successful_extractions']}")
            self.logger.info(f"المحاولات الفاشلة: {self.stats['failed_attempts']}")
            self.logger.info(f"شهادات بدون أسماء (تم تجاهلها): {self.stats['no_name_found']}")
            self.logger.info(f"الصور المحملة: {self.stats['images_downloaded']}")

            if self.stats['total_attempts'] > 0:
                success_rate = (self.stats['successful_extractions'] / self.stats['total_attempts']) * 100
                self.logger.info(f"معدل النجاح (مع أسماء): {success_rate:.2f}%")

                valid_attempts = self.stats['total_attempts'] - self.stats['no_name_found']
                if valid_attempts > 0:
                    valid_success_rate = (self.stats['successful_extractions'] / valid_attempts) * 100
                    self.logger.info(f"معدل النجاح (من الشهادات الصحيحة): {valid_success_rate:.2f}%")
            
            self.logger.info("تم إنهاء عملية الاستخراج بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنهاء العملية: {str(e)}")
    
    def get_stats(self):
        """الحصول على الإحصائيات الحالية"""
        return self.stats.copy()

def print_banner():
    """طباعة شعار الأداة"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    أداة استخراج شهادات الأزهر                    ║
    ║                     للاستخدام الحكومي المصرح                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    try:
        harvester = CertificateHarvester()
        
        print("\nخيارات التشغيل:")
        print("1. استخراج عشوائي")
        print("2. استخراج متسلسل")
        print("3. استخراج نطاق محدد")
        
        choice = input("\nاختر نوع الاستخراج (1-3): ").strip()
        
        if choice == "1":
            num_attempts = int(input("عدد المحاولات العشوائية: "))
            harvester.start_extraction(num_attempts=num_attempts, random_mode=True)
            
        elif choice == "2":
            start_id = int(input(f"رقم البداية (افتراضي {MIN_ID}): ") or MIN_ID)
            num_attempts = int(input("عدد المحاولات: "))
            harvester.start_extraction(num_attempts=num_attempts, random_mode=False, start_id=start_id)
            
        elif choice == "3":
            start_id = int(input("رقم البداية: "))
            end_id = int(input("رقم النهاية: "))
            num_attempts = end_id - start_id + 1
            harvester.start_extraction(num_attempts=num_attempts, random_mode=False, 
                                     start_id=start_id, end_id=end_id)
        else:
            print("خيار غير صحيح")
            return
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\nخطأ في تشغيل البرنامج: {str(e)}")

if __name__ == "__main__":
    main()
