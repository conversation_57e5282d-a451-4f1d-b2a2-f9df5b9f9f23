@echo off
chcp 65001 > nul
title أداة استخراج شهادات الأزهر

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    أداة استخراج شهادات الأزهر                    ║
echo ║                     للاستخدام الحكومي المصرح                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo اختر العملية المطلوبة:
echo 1. تثبيت المتطلبات
echo 2. تشغيل الأداة الرئيسية
echo 3. تشغيل الاختبارات
echo 4. تشغيل اختبار سريع
echo 5. خروج
echo.

set /p choice="اختيارك (1-5): "

if "%choice%"=="1" goto INSTALL
if "%choice%"=="2" goto RUN_MAIN
if "%choice%"=="3" goto RUN_TESTS
if "%choice%"=="4" goto RUN_QUICK_TEST
if "%choice%"=="5" goto EXIT
goto INVALID

:INSTALL
echo.
echo تثبيت المتطلبات...
python install.py
pause
goto MENU

:RUN_MAIN
echo.
echo تشغيل الأداة الرئيسية...
python main.py
pause
goto MENU

:RUN_TESTS
echo.
echo تشغيل الاختبارات...
python run_tests.py
pause
goto MENU

:RUN_QUICK_TEST
echo.
echo تشغيل اختبار سريع...
python quick_test.py
pause
goto MENU

:INVALID
echo اختيار غير صحيح، حاول مرة أخرى.
pause
goto MENU

:EXIT
echo وداعاً!
pause
exit
