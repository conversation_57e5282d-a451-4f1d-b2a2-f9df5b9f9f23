@echo off
chcp 65001 > nul
title Al-Azhar Certificate Extraction Tool

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Al-Azhar Certificate Extraction Tool          ║
echo ║                   For Authorized Government Use              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo Choose the required operation:
echo 1. Install requirements
echo 2. Run main tool
echo 3. Run tests
echo 4. Run quick test
echo 5. Exit
echo.

set /p choice="Your choice (1-5): "

if "%choice%"=="1" goto INSTALL
if "%choice%"=="2" goto RUN_MAIN
if "%choice%"=="3" goto RUN_TESTS
if "%choice%"=="4" goto RUN_QUICK_TEST
if "%choice%"=="5" goto EXIT
goto INVALID

:INSTALL
echo.
echo Installing requirements...
python install.py
pause
goto MENU

:RUN_MAIN
echo.
echo Running main tool...
python main.py
pause
goto MENU

:RUN_TESTS
echo.
echo Running tests...
python run_tests.py
pause
goto MENU

:RUN_QUICK_TEST
echo.
echo Running quick test...
python quick_test.py
pause
goto MENU

:INVALID
echo Invalid choice, please try again.
pause
goto MENU

:EXIT
echo Goodbye!
pause
exit
