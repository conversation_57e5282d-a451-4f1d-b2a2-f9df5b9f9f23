import os
import logging
import random
import time
from datetime import datetime
from config import OUTPUT_DIR, IMAGES_DIR, LOG_FILE, LOG_LEVEL

def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(IMAGES_DIR, exist_ok=True)

def setup_logging():
    """إعداد نظام التسجيل"""
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def generate_random_id(min_id, max_id):
    """توليد رقم ID عشوائي"""
    return random.randint(min_id, max_id)

def get_current_timestamp():
    """الحصول على الوقت الحالي"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def safe_sleep(seconds):
    """توقف آمن مع إمكانية المقاطعة"""
    time.sleep(seconds)

def clean_text(text):
    """تنظيف النص من المسافات الزائدة والأحرف الخاصة"""
    if text:
        return text.strip().replace('\n', ' ').replace('\r', ' ')
    return ""

def validate_id(id_value):
    """التحقق من صحة رقم ID"""
    try:
        id_int = int(id_value)
        return 1000000 <= id_int <= 9999999
    except (ValueError, TypeError):
        return False
