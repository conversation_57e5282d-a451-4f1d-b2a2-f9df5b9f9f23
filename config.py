# إعدادات الأداة
import os

# إعدادات الموقع
BASE_URL = "https://azhar.gov.eg/certificates/success2.aspx"
TIMEOUT = 30  # ثواني
MAX_RETRIES = 3
DELAY_BETWEEN_REQUESTS = 2  # ثواني

# إعدادات نطاق الأرقام
MIN_ID = 1000000  # أقل رقم ID
MAX_ID = 9999999  # أعلى رقم ID

# إعدادات الملفات
OUTPUT_DIR = "output"
IMAGES_DIR = os.path.join(OUTPUT_DIR, "images")
EXCEL_FILE = os.path.join(OUTPUT_DIR, "certificates_data.xlsx")

# إعدادات Excel
EXCEL_COLUMNS = [
    'ID',
    'اسم الطالب',
    'المعهد/الكلية',
    'نوع الشهادة',
    'سنة الامتحان',
    'حالة الطالب',
    'الرقم القومي',
    'تاريخ الميلاد',
    'النوع',
    'محافظة الميلاد',
    'الإدارة التعليمية',
    'تاريخ الاستخراج',
    'مسار الصورة',
    'حالة الاستخراج'
]

# إعدادات المتصفح
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
]

# إعدادات التسجيل
LOG_LEVEL = "INFO"
LOG_FILE = os.path.join(OUTPUT_DIR, "extraction.log")
