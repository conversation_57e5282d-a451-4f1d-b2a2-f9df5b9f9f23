# Tool Configuration
import os

# Website Settings
BASE_URL = "https://azhar.gov.eg/certificates/success2.aspx"
TIMEOUT = 30  # seconds
MAX_RETRIES = 3
DELAY_BETWEEN_REQUESTS = 2  # seconds

# ID Range Settings
MIN_ID = 1000000  # minimum ID number
MAX_ID = 9999999  # maximum ID number

# File Settings
OUTPUT_DIR = "output"
IMAGES_DIR = os.path.join(OUTPUT_DIR, "images")
EXCEL_FILE = os.path.join(OUTPUT_DIR, "certificates_data.xlsx")

# Excel Settings
EXCEL_COLUMNS = [
    'ID',
    'Student Name',
    'Institute/College',
    'Certificate Type',
    'Examination Year',
    'Student Status',
    'National ID',
    'Birth Date',
    'Gender',
    'Birth Governorate',
    'Educational Administration',
    'Extraction Date',
    'Image Path',
    'Extraction Status'
]

# Browser Settings
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
]

# Logging Settings
LOG_LEVEL = "INFO"
LOG_FILE = os.path.join(OUTPUT_DIR, "extraction.log")
